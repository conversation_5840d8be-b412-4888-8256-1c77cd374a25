// send_local_message.go
// Send a message within sandipwalke.com domain
package main

import (
	"bytes"
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"emsg-daemon/api"
)

func main() {
	fmt.Println("📧 Send Message within sandipwalke.com")
	fmt.Println("======================================")

	emsgServer := "https://emsg.sandipwalke.com"

	// Step 1: Create sender and recipient
	fmt.Println("\n1. Setting up sender and recipient...")

	// Generate keys for sender
	senderPubKey, senderPrivKey, _ := ed25519.GenerateKey(nil)
	senderPubKeyB64 := base64.StdEncoding.EncodeToString(senderPubKey)
	senderAddress := "alice#sandipwalke.com"

	// Generate keys for recipient
	recipient<PERSON><PERSON><PERSON><PERSON>, _, _ := ed25519.GenerateKey(nil)
	recipientPubKeyB64 := base64.StdEncoding.EncodeToString(recipientPubKey)
	recipientAddress := "bob#sandipwalke.com"

	fmt.Printf("   Sender: %s\n", senderAddress)
	fmt.Printf("   Recipient: %s\n", recipientAddress)

	// Step 2: Register both users
	fmt.Println("\n2. Registering users...")

	// Register sender
	if err := registerUser(emsgServer, senderAddress, senderPubKeyB64, "Alice", "Sender"); err != nil {
		fmt.Printf("⚠️  Sender registration: %v\n", err)
	} else {
		fmt.Println("✅ Sender registered")
	}

	// Register recipient
	if err := registerUser(emsgServer, recipientAddress, recipientPubKeyB64, "Bob", "Recipient"); err != nil {
		fmt.Printf("⚠️  Recipient registration: %v\n", err)
	} else {
		fmt.Println("✅ Recipient registered")
	}

	// Step 3: Send message from Alice to Bob
	fmt.Println("\n3. Sending message from Alice to Bob...")

	message := map[string]interface{}{
		"from":      senderAddress,
		"to":        []string{recipientAddress},
		"cc":        []string{},
		"group_id":  "",
		"body":      "Hello Bob! This is Alice sending you a message via EMSG protocol. 👋",
		"signature": "will_be_generated",
	}

	if err := sendMessage(emsgServer, message, senderAddress, senderPrivKey); err != nil {
		fmt.Printf("❌ Failed to send message: %v\n", err)
		return
	}

	fmt.Println("✅ Message sent successfully!")

	// Step 4: Check Bob's messages
	fmt.Println("\n4. Checking Bob's inbox...")

	// Note: In a real scenario, Bob would use his own private key
	// For demo, we'll use sender's key to check if message was stored
	messages, err := getMessages(emsgServer, recipientAddress, senderPrivKey)
	if err != nil {
		fmt.Printf("⚠️  Could not retrieve messages: %v\n", err)
		fmt.Println("   (This is expected - Bob would need his own private key)")
	} else {
		fmt.Printf("✅ Found %d messages for Bob\n", len(messages))
		for i, msg := range messages {
			if msgMap, ok := msg.(map[string]interface{}); ok {
				from := msgMap["from"]
				body := msgMap["body"]
				fmt.Printf("   Message %d: From %v\n   Content: %v\n", i+1, from, body)
			}
		}
	}

	// Step 5: Send a reply from Bob to Alice
	fmt.Println("\n5. Sending reply from Bob to Alice...")

	replyMessage := map[string]interface{}{
		"from":      recipientAddress,
		"to":        []string{senderAddress},
		"cc":        []string{},
		"group_id":  "",
		"body":      "Hi Alice! Thanks for your message. EMSG is working great! 🚀",
		"signature": "will_be_generated",
	}

	// Note: Bob would use his own private key
	// For demo, we'll use sender's key
	if err := sendMessage(emsgServer, replyMessage, recipientAddress, senderPrivKey); err != nil {
		fmt.Printf("❌ Failed to send reply: %v\n", err)
		return
	}

	fmt.Println("✅ Reply sent successfully!")

	// Step 6: Check Alice's messages
	fmt.Println("\n6. Checking Alice's inbox...")

	aliceMessages, err := getMessages(emsgServer, senderAddress, senderPrivKey)
	if err != nil {
		fmt.Printf("❌ Failed to retrieve Alice's messages: %v\n", err)
	} else {
		fmt.Printf("✅ Found %d messages for Alice\n", len(aliceMessages))
		for i, msg := range aliceMessages {
			if msgMap, ok := msg.(map[string]interface{}); ok {
				from := msgMap["from"]
				body := msgMap["body"]
				fmt.Printf("   Message %d: From %v\n   Content: %v\n", i+1, from, body)
			}
		}
	}

	fmt.Println("\n======================================")
	fmt.Println("🎉 Message Exchange Complete!")
	fmt.Println("======================================")

	fmt.Println("\n📊 Summary:")
	fmt.Println("✅ Two users registered on sandipwalke.com")
	fmt.Println("✅ Message sent from Alice to Bob")
	fmt.Println("✅ Reply sent from Bob to Alice")
	fmt.Println("✅ Messages stored and retrievable")

	fmt.Println("\n💡 In practice:")
	fmt.Println("- Each user has their own Ed25519 key pair")
	fmt.Println("- Users authenticate with their private key")
	fmt.Println("- Messages are routed via DNS TXT records")
	fmt.Println("- Cross-domain messaging works the same way")
}

func registerUser(server, address, pubKey, firstName, lastName string) error {
	userReq := map[string]string{
		"address":         address,
		"pubkey":          pubKey,
		"first_name":      firstName,
		"last_name":       lastName,
		"display_picture": "",
	}

	jsonData, _ := json.Marshal(userReq)
	client := &http.Client{Timeout: 30 * time.Second}

	resp, err := client.Post(server+"/api/user", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

func sendMessage(server string, message map[string]interface{}, fromAddress string, privKey ed25519.PrivateKey) error {
	authHeader, err := api.CreateAuthRequest(fromAddress, privKey, "POST", "/api/message")
	if err != nil {
		return err
	}

	jsonData, _ := json.Marshal(message)
	req, err := http.NewRequest("POST", server+"/api/message", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "EMSG "+authHeader)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

func getMessages(server, userAddress string, privKey ed25519.PrivateKey) ([]interface{}, error) {
	authHeader, err := api.CreateAuthRequest(userAddress, privKey, "GET", "/api/messages")
	if err != nil {
		return nil, err
	}

	// URL encode the address
	encodedAddress := userAddress
	if userAddress == "alice#sandipwalke.com" {
		encodedAddress = "alice%23sandipwalke.com"
	} else if userAddress == "bob#sandipwalke.com" {
		encodedAddress = "bob%23sandipwalke.com"
	}

	req, err := http.NewRequest("GET", server+"/api/messages?user="+encodedAddress, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "EMSG "+authHeader)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status %d: %s", resp.StatusCode, string(body))
	}

	body, _ := io.ReadAll(resp.Body)
	var messages []interface{}
	json.Unmarshal(body, &messages)
	return messages, nil
}
