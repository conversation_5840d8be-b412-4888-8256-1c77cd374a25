// boltapi.go
// REST API for EMSG Daemon using BoltDB
package api

import (
	"encoding/json"
	"net/http"

	"go.etcd.io/bbolt"
	"emsg-daemon/internal/auth"
	"emsg-daemon/internal/storage"
)

// BoltAPI handler struct to hold BoltDB reference
type BoltAPI struct {
	DB *bbolt.DB
}

// Example: GET /api/user?address=alice#emsg.dev
func (api *BoltAPI) ApiGetUser(w http.ResponseWriter, r *http.Request) {
	address := r.URL.Query().Get("address")
	if address == "" {
		http.Error(w, "missing address", http.StatusBadRequest)
		return
	}
	user, err := storage.GetUserBolt(api.DB, address)
	if err != nil {
		http.Error(w, "user not found", http.StatusNotFound)
		return
	}
	json.NewEncoder(w).Encode(user)
}

// Example: POST /api/user (register user with profile fields)
func (api *BoltAPI) ApiRegisterUser(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Address        string `json:"address"`
		PubKey         string `json:"pubkey"`
		FirstName      string `json:"first_name"`
		MiddleName     string `json:"middle_name"`
		LastName       string `json:"last_name"`
		DisplayPicture string `json:"display_picture"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "invalid request", http.StatusBadRequest)
		return
	}
	user, err := auth.RegisterUser(req.Address, req.PubKey, req.FirstName, req.MiddleName, req.LastName, req.DisplayPicture)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if err := storage.StoreUserBolt(api.DB, user); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusCreated)
}

// StartBoltServer starts the REST API server with BoltDB
func StartBoltServer(db *bbolt.DB) {
	api := &BoltAPI{DB: db}
	http.HandleFunc("/api/user", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet {
			api.ApiGetUser(w, r)
		} else if r.Method == http.MethodPost {
			api.ApiRegisterUser(w, r)
		} else {
			http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		}
	})
	// TODO: Add more endpoints for messages, groups, etc.
	go http.ListenAndServe(":8080", nil)
}
