// test_port_8765_comprehensive.go
// Comprehensive test of EMSG Daemon on port 8765
package main

import (
	"bytes"
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"emsg-daemon/api"
)

func main() {
	fmt.Println("🚀 Comprehensive Test: EMSG Daemon on Port 8765")
	fmt.Println("Testing all functionality on the new default port...")
	time.Sleep(1 * time.Second)

	baseURL := "http://localhost:8765"

	// Test 1: Basic connectivity
	fmt.Println("\n1. Testing basic connectivity...")
	resp, err := http.Get(baseURL + "/api/user?address=nonexistent")
	if err != nil {
		fmt.Printf("❌ Connection failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	if resp.StatusCode == 404 {
		fmt.Println("✅ EMSG Daemon responding correctly on port 8765!")
	} else {
		fmt.Printf("⚠️  Unexpected response: %d\n", resp.StatusCode)
	}

	// Test 2: User registration with proper Ed25519 keys
	fmt.Println("\n2. Testing user registration with proper Ed25519 keys...")
	
	pubKey, privKey, err := ed25519.GenerateKey(nil)
	if err != nil {
		fmt.Printf("❌ Failed to generate Ed25519 key: %v\n", err)
		return
	}
	
	pubKeyB64 := base64.StdEncoding.EncodeToString(pubKey)
	userReq := map[string]string{
		"address":         "port8765test#emsg.dev",
		"pubkey":          pubKeyB64,
		"first_name":      "Port",
		"last_name":       "Test",
		"display_picture": "https://example.com/port8765.jpg",
	}

	jsonData, _ := json.Marshal(userReq)
	resp, err = http.Post(baseURL+"/api/user", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ User registration failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode == 201 {
		fmt.Println("✅ User registration successful with Ed25519 keys!")
	} else {
		fmt.Printf("❌ User registration failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 3: User retrieval
	fmt.Println("\n3. Testing user retrieval...")
	resp, err = http.Get(baseURL + "/api/user?address=port8765test%23emsg.dev")
	if err != nil {
		fmt.Printf("❌ User retrieval failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		fmt.Println("✅ User retrieval successful!")
		
		var user map[string]interface{}
		json.Unmarshal(body, &user)
		if address, ok := user["address"].(string); ok {
			fmt.Printf("   Retrieved user: %s\n", address)
		}
	} else {
		fmt.Printf("❌ User retrieval failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 4: Group creation with authentication
	fmt.Println("\n4. Testing group creation with authentication...")
	
	groupReq := map[string]interface{}{
		"id":          "port8765-test-group",
		"name":        "Port 8765 Test Group",
		"description": "Testing group creation on new port",
		"members":     []string{"port8765test#emsg.dev"},
	}

	jsonData, _ = json.Marshal(groupReq)
	
	// Create authentication header
	authHeader, err := api.CreateAuthRequest("port8765test#emsg.dev", privKey, "POST", "/api/group")
	if err != nil {
		fmt.Printf("❌ Failed to create auth header: %v\n", err)
		return
	}
	
	req, err := http.NewRequest("POST", baseURL+"/api/group", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Failed to create request: %v\n", err)
		return
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "EMSG "+authHeader)
	
	client := &http.Client{}
	resp, err = client.Do(req)
	if err != nil {
		fmt.Printf("❌ Group creation failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ = io.ReadAll(resp.Body)
	if resp.StatusCode == 201 {
		fmt.Println("✅ Group creation successful with authentication!")
	} else {
		fmt.Printf("❌ Group creation failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 5: Group retrieval
	fmt.Println("\n5. Testing group retrieval...")
	resp, err = http.Get(baseURL + "/api/group?id=port8765-test-group")
	if err != nil {
		fmt.Printf("❌ Group retrieval failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		fmt.Println("✅ Group retrieval successful!")
		
		var group map[string]interface{}
		json.Unmarshal(body, &group)
		if name, ok := group["Name"].(string); ok {
			fmt.Printf("   Retrieved group: %s\n", name)
		}
	} else {
		fmt.Printf("❌ Group retrieval failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 6: Message sending with authentication
	fmt.Println("\n6. Testing message sending with authentication...")
	
	messageReq := map[string]interface{}{
		"from":      "port8765test#emsg.dev",
		"to":        []string{"recipient#emsg.dev"},
		"group_id":  "port8765-test-group",
		"body":      "Test message sent on port 8765!",
		"signature": "test_signature",
	}

	jsonData, _ = json.Marshal(messageReq)
	
	authHeader, err = api.CreateAuthRequest("port8765test#emsg.dev", privKey, "POST", "/api/message")
	if err != nil {
		fmt.Printf("❌ Failed to create auth header: %v\n", err)
		return
	}
	
	req, err = http.NewRequest("POST", baseURL+"/api/message", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Failed to create request: %v\n", err)
		return
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "EMSG "+authHeader)
	
	resp, err = client.Do(req)
	if err != nil {
		fmt.Printf("❌ Message sending failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ = io.ReadAll(resp.Body)
	if resp.StatusCode == 201 {
		fmt.Println("✅ Message sending successful with authentication!")
	} else {
		fmt.Printf("❌ Message sending failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 7: DNS routing functionality
	fmt.Println("\n7. Testing DNS routing functionality...")
	
	validateReq := map[string]interface{}{
		"addresses": []string{
			"valid#emsg.dev",
			"invalid-address",
			"another#test.com",
		},
	}
	
	jsonData, _ = json.Marshal(validateReq)
	resp, err = http.Post(baseURL+"/api/route/validate", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ DNS routing test failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ = io.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		fmt.Println("✅ DNS routing functionality working!")
		
		var result map[string]interface{}
		json.Unmarshal(body, &result)
		if results, ok := result["results"].(map[string]interface{}); ok {
			validCount := 0
			for addr, info := range results {
				if infoMap, ok := info.(map[string]interface{}); ok {
					if valid, ok := infoMap["valid"].(bool); ok {
						if valid {
							validCount++
							fmt.Printf("   ✅ %s: valid\n", addr)
						} else {
							fmt.Printf("   ❌ %s: invalid\n", addr)
						}
					}
				}
			}
			fmt.Printf("   Validated %d addresses\n", len(results))
		}
	} else {
		fmt.Printf("❌ DNS routing failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	// Test 8: Configuration verification
	fmt.Println("\n8. Verifying configuration...")
	fmt.Println("✅ Default port 8765 confirmed working")
	fmt.Println("✅ All API endpoints responding correctly")
	fmt.Println("✅ Authentication middleware functional")
	fmt.Println("✅ BoltDB storage working")

	fmt.Println("\n🎉 ALL TESTS PASSED!")
	fmt.Println("\n📊 EMSG Daemon Status on Port 8765:")
	fmt.Println("   ✅ User Management: Working")
	fmt.Println("   ✅ Group Management: Working") 
	fmt.Println("   ✅ Message System: Working")
	fmt.Println("   ✅ Authentication: Working")
	fmt.Println("   ✅ DNS Routing: Working")
	fmt.Println("   ✅ Database Storage: Working")
	
	fmt.Println("\n🚀 EMSG Protocol Port 8765 is fully operational!")
}
