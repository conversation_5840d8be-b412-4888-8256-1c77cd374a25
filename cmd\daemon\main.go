// main.go
// Starts the EMSG Daemon
package main

import (
	"fmt"
	"log"
	"time"

	// Internal packages

	"emsg-daemon/internal/config"
)

func main() {
	fmt.Println("Starting EMSG Daemon...")

	// Load config
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	fmt.Printf("Loaded config for domain: %s\n", cfg.Domain)

	// Initialize database (disabled for now due to CGO requirement)
	fmt.Println("Database initialization skipped (SQLite requires CGO).")
	var db interface{} = nil

	// Initialize router, group, auth, message modules (stubs)
	fmt.Println("Router, group, auth, and message modules ready (stub).")

	// Start REST API server (disabled for now due to database dependency)
	fmt.Println("REST API server disabled (requires database).")

	// Block forever (simulate daemon)
	for {
		time.Sleep(60 * time.Second)
	}
}

// Docker build command
// RUN docker build -t emsg-daemon .
// Docker run command
// docker run -p 8080:8080 emsg-daemon
